<?php

namespace OvonGroup\ManufacturerLogos\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use OvonGroup\ManufacturerLogos\Database\Factories\ManufacturerFactory;

class Manufacturer extends Model
{
    use HasFactory;

    protected static function newFactory(): ManufacturerFactory
    {
        return ManufacturerFactory::new();
    }

    public function logoUrl()
    {
        return Storage::disk('public')->url($this->logo);
    }

    public static function fromName(string $make): ?self
    {
        return static::where('name', $make)->first();
    }
}
