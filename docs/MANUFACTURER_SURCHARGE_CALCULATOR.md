# Manufacturer Surcharge Calculator

The Manufacturer Surcharge Calculator provides a systematic way to calculate surcharges for warranties based on manufacturer and account settings.

## Overview

The calculator follows these rules:

1. **Account-Specific Surcharge**: If the account has a `ManufacturerSurcharge` record for the specific manufacturer, use those percentages
2. **No Surcharge**: If no account-specific surcharge exists, apply zero surcharge

## Usage

### Basic Usage

```php
use App\Services\Surcharge\ManufacturerSurchargeRepository;

$calculator = new ManufacturerSurchargeRepository();
$result = $calculator->lookupSurcharge($sale);

// Get surcharge percentages
echo "Admin Fee: {$result->adminFeePercentage}%";
echo "Provision: {$result->provisionPercentage}%";
echo "Selling Price: {$result->sellingPricePercentage}%";
echo "Source: {$result->source}";
```

### Calculate Actual Surcharge Amounts

```php
$surcharges = $calculator->calculateWarrantySurcharges(
    $sale,
    $adminFee = 20,
    $provision = 100,
    $sellingPrice = 200
);

echo "Admin Fee Surcharge: £{$surcharges['admin_fee_surcharge']}";
echo "Provision Surcharge: £{$surcharges['provision_surcharge']}";
echo "Selling Price Surcharge: £{$surcharges['selling_price_surcharge']}";
echo "Total Surcharge: £{$surcharges['total_surcharge']}";
echo "Source: {$surcharges['surcharge_source']}"; // 'account_specific' or 'none'
```

## Configuration

### Account-Specific Surcharges

Create account-specific surcharges using the `ManufacturerSurcharge` model:

```php
ManufacturerSurcharge::create([
    'account_id' => $account->id,
    'manufacturer_id' => $manufacturer->id,
    'warranty_admin_fee_percentage' => 100,
    'warranty_provision_percentage' => 75,
    'warranty_selling_price_percentage' => 50,
]);
```

## Database Schema

The `manufacturer_surcharges` table structure:

```sql
CREATE TABLE manufacturer_surcharges (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    account_id BIGINT UNSIGNED NOT NULL,
    manufacturer_id BIGINT UNSIGNED NOT NULL,
    warranty_admin_fee_percentage DECIMAL(4,1) NULL,
    warranty_provision_percentage DECIMAL(4,1) NULL,
    warranty_selling_price_percentage DECIMAL(4,1) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (manufacturer_id) REFERENCES manufacturers(id) ON DELETE CASCADE
);
```

## Testing

The calculator includes comprehensive Pest tests covering all scenarios:

Run the unit tests:
```bash
php artisan test tests/Unit/Services/Surcharge/
```

The tests cover:
- Account-specific surcharge calculation
- Accounts without specific surcharges (no surcharge)
- Sales without manufacturers
- Actual surcharge amount calculations

## Examples

### Scenario 1: Account-Specific Surcharge
- Account has specific surcharge for BMW: 100% admin fee, 75% provision, 50% selling price
- Result: Uses account-specific percentages

### Scenario 2: No Account-Specific Surcharge
- Account does not have a specific surcharge for the manufacturer
- Result: 0% surcharge for all components

### Scenario 3: Sale Without Manufacturer
- Sale has no manufacturer assigned
- Result: 0% surcharge
