<?php

namespace Database\Factories;

use App\Models\PayLaterPlan;
use App\Models\Sale;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PayLaterAgreement>
 */
class PayLaterAgreementFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'payable_type' => 'sale',
            'payable_id' => Sale::factory(),
            'is_approved' => false,
            'dealer_payment' => null,
            'loan_amount' => null,
            'token' => Str::uuid()->toString(),
            'url' => 'https://some-redirect-url.com',
        ];
    }

    public function approved(bool $approved = true)
    {
        return $this->state([
            'is_approved' => $approved,
        ]);
    }

    public function approvedAndPlanSelected()
    {
        return $this->approved()
            ->state([
                'pay_later_plan_id' => PayLaterPlan::factory(),
                'dealer_payment' => 111.11,
                'loan_amount' => 1234.56,
            ]);
    }

    public function completed()
    {
        return $this->approvedAndPlanSelected()
            ->state([
                'status' => 'completed',
            ]);
    }
}
