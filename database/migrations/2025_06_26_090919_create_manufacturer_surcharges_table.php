<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('manufacturer_surcharges', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Account::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(\OvonGroup\ManufacturerLogos\Models\Manufacturer::class)->constrained()->cascadeOnDelete();
            $table->decimal('warranty_admin_fee_percentage', 4, 1)->nullable();
            $table->decimal('warranty_provision_percentage', 4, 1)->nullable();
            $table->decimal('warranty_selling_price_percentage', 4, 1)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('manufacturer_surcharges');
    }
};
