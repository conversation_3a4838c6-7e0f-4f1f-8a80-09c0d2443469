<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pay_later_services', function (Blueprint $table) {
            $table->renameColumn('deposit_amount', 'dealer_payment');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pay_later_services', function (Blueprint $table) {
            $table->renameColumn('dealer_payment', 'deposit_amount');
        });
    }
};
