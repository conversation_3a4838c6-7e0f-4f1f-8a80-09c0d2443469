<?php

use App\Console\Commands\SendCustomerPaymentMethodSetupReminderNotifications;
use App\Models\Sale;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Notification;

test('reminders are not sent for sales that do not need payment setup', function () {
    Notification::fake();

    Carbon::setTestNow(Carbon::now()->startOfSecond());

    Sale::factory()
        ->confirmedWithProducts()
        ->create();

    $this->artisan(SendCustomerPaymentMethodSetupReminderNotifications::class)->assertSuccessful();

    Notification::assertNothingSent();
});

test('reminders are not sent for sales that have pay later agreement set up', function () {
    Notification::fake();

    Carbon::setTestNow(Carbon::now()->startOfSecond());

    Sale::factory()
        ->confirmedWithProducts()
        ->withPayLaterAgreementCompleted()
        ->create();

    $this->artisan(SendCustomerPaymentMethodSetupReminderNotifications::class)->assertSuccessful();

    Notification::assertNothingSent();
});

test('reminders are not sent for subscription sales that have mandate completed', function () {
    Notification::fake();

    Carbon::setTestNow(Carbon::now()->startOfSecond());

    Sale::factory()
        ->subscription()
        ->withDirectDebitMandateCompleted()
        ->create();

    $this->artisan(SendCustomerPaymentMethodSetupReminderNotifications::class)->assertSuccessful();

    Notification::assertNothingSent();
});

test('reminders are not sent for subscription sales that have mandate pending', function () {
    Notification::fake();

    Carbon::setTestNow(Carbon::now()->startOfSecond());

    Sale::factory()
        ->subscription()
        ->withDirectDebitMandatePending()
        ->create();

    $this->artisan(SendCustomerPaymentMethodSetupReminderNotifications::class)->assertSuccessful();

    $this->artisan(SendCustomerPaymentMethodSetupReminderNotifications::class)->assertSuccessful();
});
