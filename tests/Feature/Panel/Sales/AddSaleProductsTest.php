<?php

use App\Enums\PaymentProvider;
use App\Filament\Resources\SaleResource\Pages\AddSaleProducts;
use App\Models\Account;
use App\Models\AccountBreakdownProduct;
use App\Models\AccountServicePlanProduct;
use App\Models\AccountWarrantyProduct;
use App\Models\Dealership;
use App\Models\ManufacturerSurcharge;
use App\Models\PayLaterAgreement;
use App\Models\PayLaterPlan;
use App\Models\Sale;
use App\Models\User;
use App\Notifications\CustomerRequiresPaymentMethodNotification;
use App\Notifications\CustomerWelcomeAndDocumentNotification;
use App\Services\Payments\PayLater\ApplicationRedirectData;
use App\Services\Payments\PayLater\PayLaterPaymentProcessor;
use App\Services\Payments\PayLater\PlanBreakdownData;
use App\Services\Payments\PayLater\PlanBreakdownPaymentData;
use App\Services\Payments\PayLater\PreApprovalData;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Notification;
use Mockery\MockInterface;

use function Pest\Laravel\actingAs;
use function Pest\Laravel\mock;
use function Pest\Livewire\livewire;

describe('adding products to the sale', function () {

    beforeEach(function () {
        Dealership::unsetEventDispatcher();
        Notification::fake();
    });

    function mockPreApprovalCall(bool $approved = true)
    {
        mock(PayLaterPaymentProcessor::class, function (MockInterface $mock) use ($approved) {
            $mock->shouldReceive('forAccount')
                ->once()
                ->andReturn($mock);

            $mock->shouldReceive('preApprove')
                ->once()
                ->andReturnUsing(fn () => new PreApprovalData(
                    approved: $approved,
                    message: null,
                ));
        });
    }

    function assertNoPreApprovalCall()
    {
        mock(PayLaterPaymentProcessor::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('preApprove');
        });
    }

    test('users without permission cannot render page', function () {
        $account = Account::factory()->create();

        $sale = Sale::factory()->recycle($account)->create();
        actingAs(User::factory()->recycle($account)->create());

        livewire(AddSaleProducts::class, ['record' => $sale->getRouteKey()])
            ->assertForbidden();
    });

    test('only dealer users from the same account can see the sale', function () {
        $accountA = Account::factory()->create();
        $accountB = Account::factory()->create();

        actingAs(User::factory()->recycle($accountA)->create()->givePermissionTo('sales.view', 'sales.create'));

        $sale = Sale::factory()->recycle($accountB)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])->assertNotFound();

    })->throws(ModelNotFoundException::class);

    test('the form shows only warranties if the dealer account is only set up for warranties', function () {
        $account = Account::factory()->create();

        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

        $sale = Sale::factory()->recycle($account)->create();

        AccountWarrantyProduct::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertSuccessful()
            ->assertFormFieldIsVisible('warranty.account_warranty_product_id')
            ->assertFormFieldIsHidden('breakdownPlan.account_breakdown_product_id')
            ->assertFormFieldIsHidden('servicePlan.account_service_plan_product_id');
    });

    test('the form shows only breakdown policies if the dealer account is only set up for warranties', function () {
        $account = Account::factory()->create();

        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

        $sale = Sale::factory()->recycle($account)->create();

        AccountBreakdownProduct::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('warranty.account_warranty_product_id')
            ->assertFormFieldIsVisible('breakdownPlan.account_breakdown_product_id')
            ->assertFormFieldIsHidden('servicePlan.account_service_plan_product_id');
    });

    test('the form shows only service plans if the dealer account is only set up for warranties', function () {
        $account = Account::factory()->create();

        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

        $sale = Sale::factory()->recycle($account)->create();

        AccountServicePlanProduct::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('warranty.account_warranty_product_id')
            ->assertFormFieldIsHidden('breakdownPlan.account_breakdown_product_id')
            ->assertFormFieldIsVisible('servicePlan.account_service_plan_product_id');
    });

    test('dealers can add just a warranty', function () {
        Carbon::setTestNow(now()->startOfSecond());

        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        $accountWarrantyProduct = AccountWarrantyProduct::factory()->recycle($account)->create();
        AccountBreakdownProduct::factory()->recycle($account)->create();
        AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();

        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('warranty.selling_price')
            ->fillForm(['warranty.account_warranty_product_id' => $accountWarrantyProduct->id])
            ->assertFormFieldIsVisible('warranty.selling_price')
            ->assertFormSet(['warranty.selling_price' => $accountWarrantyProduct->selling_price])
            ->fillForm(['warranty.selling_price' => 111])
            ->call('save');

        Notification::assertNotSentTo($sale->customer, CustomerRequiresPaymentMethodNotification::class);
        Notification::assertSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);

        expect($sale->refresh())
            ->confirmed_at->toEqual(now());

        expect($sale->warranty)
            ->product_id->toBe($accountWarrantyProduct->product_id)
            ->is_self_funded->toBe($sale->account->warranty_self_funded)
            ->provision->toBe($accountWarrantyProduct->provision)
            ->admin_fee->toBe($accountWarrantyProduct->admin_fee)
            ->selling_price->toBe(111)
            ->and($sale->breakdownPlan)->toBeNull()
            ->and($sale->servicePlan)->toBeNull();
    });

    test('adding a warranty applies the correct manufacturer surcharge for the dealer', function () {
        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        $accountWarrantyProduct = AccountWarrantyProduct::factory()->recycle($account)->create();

        $surchargedManufacturer = \OvonGroup\ManufacturerLogos\Models\Manufacturer::factory()
            ->create([
                'name' => 'Land Rover',
                'slug' => 'land-rover',
            ]);

        ManufacturerSurcharge::factory()
            ->recycle($account)
            ->for($surchargedManufacturer)
            ->create([
                'warranty_admin_fee_percentage' => 100,
                'warranty_provision_percentage' => 75,
                'warranty_selling_price_percentage' => 50,
            ]);

        $sale = Sale::factory()
            ->for($surchargedManufacturer)
            ->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertSuccessful()
            ->fillForm(['warranty.account_warranty_product_id' => $accountWarrantyProduct->id])
            ->assertFormSet(['warranty.selling_price' => $accountWarrantyProduct->selling_price * 1.5])
            ->call('save');

        expect($sale->warranty)
            ->product_id->toBe($accountWarrantyProduct->product_id)
            ->is_self_funded->toBe($sale->account->warranty_self_funded)
            ->admin_fee->toBe($accountWarrantyProduct->admin_fee * 2)
            ->provision->toBe($accountWarrantyProduct->provision * 1.75)
            ->selling_price->toBe($accountWarrantyProduct->selling_price * 1.5);
    });

    test('dealers can add just a breakdown plan', function () {
        Carbon::setTestNow(now()->startOfSecond());

        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        $accountBreakdownProduct = AccountBreakdownProduct::factory()->recycle($account)->create();
        AccountWarrantyProduct::factory()->recycle($account)->create();
        AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();

        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('breakdownPlan.selling_price')
            ->fillForm(['breakdownPlan.account_breakdown_product_id' => $accountBreakdownProduct->id])
            ->assertFormFieldIsVisible('breakdownPlan.selling_price')
            ->assertFormSet(['breakdownPlan.selling_price' => $accountBreakdownProduct->selling_price])
            ->fillForm(['breakdownPlan.selling_price' => 222])
            ->call('save');

        Notification::assertNotSentTo($sale->customer, CustomerRequiresPaymentMethodNotification::class);
        Notification::assertSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);

        expect($sale->refresh())
            ->confirmed_at->toEqual(now());

        expect($sale->warranty)->toBeNull()
            ->and($sale->breakdownPlan)
            ->breakdown_product_id->toBe($accountBreakdownProduct->breakdown_product_id)
            ->is_self_funded->toBe($sale->account->breakdown_self_funded)
            ->provision->toBe($accountBreakdownProduct->provision)
            ->admin_fee->toBe($accountBreakdownProduct->admin_fee)
            ->selling_price->toBe(222)
            ->and($sale->servicePlan)->toBeNull();
    });

    test('dealers can add just a service plan', function () {
        Carbon::setTestNow(now()->startOfSecond());

        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        AccountWarrantyProduct::factory()->recycle($account)->create();
        AccountBreakdownProduct::factory()->recycle($account)->create();
        $accountServicePlanProduct = AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();
        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('servicePlan.selling_price')
            ->fillForm(['servicePlan.account_service_plan_product_id' => $accountServicePlanProduct->id])
            ->assertFormFieldIsVisible('servicePlan.selling_price')
            ->assertFormSet(['servicePlan.selling_price' => $accountServicePlanProduct->selling_price])
            ->fillForm(['servicePlan.selling_price' => 333])
            ->call('save');

        Notification::assertNotSentTo($sale->customer, CustomerRequiresPaymentMethodNotification::class);
        Notification::assertSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);

        expect($sale->refresh())
            ->confirmed_at->toEqual(now());

        expect($sale->warranty)->toBeNull()
            ->and($sale->breakdownPlan)->toBeNull()
            ->and($sale->servicePlan)
            ->service_plan_product_id->toBe($accountServicePlanProduct->service_plan_product_id)
            ->admin_fee->toBe($accountServicePlanProduct->admin_fee)
            ->selling_price->toBe(333)
            ->duration_years->toBe($accountServicePlanProduct->duration_years)
            ->serviceTypes->count()->toBe(3)
            ->serviceTypes->get(0)->pivot->limit->toBe(1)
            ->serviceTypes->get(1)->pivot->limit->toBe(2)
            ->serviceTypes->get(2)->pivot->limit->toBe(3);
    });

    test('dealers can add all products', function (bool $sendContractEmails) {
        Carbon::setTestNow(now()->startOfSecond());

        $account = Account::factory()->create([
            'send_contract_emails' => $sendContractEmails,
        ]);
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        $accountWarrantyProduct = AccountWarrantyProduct::factory()->recycle($account)->create();
        $accountBreakdownProduct = AccountBreakdownProduct::factory()->recycle($account)->create();
        $accountServicePlanProduct = AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();
        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('is_pay_later_agreement')
            ->assertFormFieldIsHidden('warranty.selling_price')
            ->assertFormFieldIsHidden('breakdownPlan.selling_price')
            ->assertFormFieldIsHidden('servicePlan.selling_price')
            ->fillForm([
                'warranty.account_warranty_product_id' => $accountWarrantyProduct->id,
                'breakdownPlan.account_breakdown_product_id' => $accountBreakdownProduct->id,
                'servicePlan.account_service_plan_product_id' => $accountServicePlanProduct->id,
            ])
            ->assertFormFieldIsVisible('warranty.selling_price')
            ->assertFormFieldIsVisible('breakdownPlan.selling_price')
            ->assertFormFieldIsVisible('servicePlan.selling_price')
            ->assertFormSet([
                'warranty.selling_price' => $accountWarrantyProduct->selling_price,
                'breakdownPlan.selling_price' => $accountBreakdownProduct->selling_price,
                'servicePlan.selling_price' => $accountServicePlanProduct->selling_price,
            ])
            ->fillForm([
                'warranty.selling_price' => 111,
                'breakdownPlan.selling_price' => 222,
                'servicePlan.selling_price' => 333,
            ])
            ->assertFormSet([
                'total' => 666,
                'payLaterAgreement.dealer_payment' => 0,
                'payLaterAgreement.loan_amount' => 0,
            ])
            ->call('save');

        if ($sendContractEmails) {
            Notification::assertSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);
        } else {
            Notification::assertNothingSent();
        }

        Notification::assertNotSentTo($sale->customer, CustomerRequiresPaymentMethodNotification::class);

        expect($sale->refresh())
            ->confirmed_at->toEqual(now());

        expect($sale->warranty)
            ->product_id->toBe($accountWarrantyProduct->product_id)
            ->is_self_funded->toBe($sale->account->warranty_self_funded)
            ->provision->toBe($accountWarrantyProduct->provision)
            ->admin_fee->toBe($accountWarrantyProduct->admin_fee)
            ->selling_price->toBe(111)
            ->isRecurring()->toBeFalse()
            ->and($sale->breakdownPlan)
            ->breakdown_product_id->toBe($accountBreakdownProduct->breakdown_product_id)
            ->is_self_funded->toBe($sale->account->breakdown_self_funded)
            ->provision->toBe($accountBreakdownProduct->provision)
            ->admin_fee->toBe($accountBreakdownProduct->admin_fee)
            ->selling_price->toBe(222)
            ->and($sale->servicePlan)
            ->service_plan_product_id->toBe($accountServicePlanProduct->service_plan_product_id)
            ->admin_fee->toBe($accountServicePlanProduct->admin_fee)
            ->selling_price->toBe(333)
            ->duration_years->toBe($accountServicePlanProduct->duration_years)
            ->serviceTypes->count()->toBe(3)
            ->serviceTypes->get(0)->pivot->limit->toBe(1)
            ->serviceTypes->get(1)->pivot->limit->toBe(2)
            ->serviceTypes->get(2)->pivot->limit->toBe(3);
    })->with([
        'send contract emails' => [true],
        'do not send contract emails' => [false],
    ]);

    test('dealers can add a subscription warranty', function ($upfrontDealerPayment) {
        \Illuminate\Support\Carbon::setTestNow(now()->startOfHour());

        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
        $accountWarrantyProduct = AccountWarrantyProduct::factory()->subscription()->recycle($account)->create([
            'selling_price' => $upfrontDealerPayment,
        ]);
        AccountBreakdownProduct::factory()->recycle($account)->create();
        AccountServicePlanProduct::factory()->recycle($account)->withAllServiceTypes()->create();

        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertSuccessful()
            ->assertFormFieldIsHidden('warranty.selling_price')
            ->fillForm(['warranty.account_warranty_product_id' => $accountWarrantyProduct->id])
            ->assertFormFieldIsVisible('warranty.selling_price')
            ->call('save');

        $sale->refresh();

        Notification::assertSentTo($sale->customer, CustomerRequiresPaymentMethodNotification::class);
        Notification::assertNotSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);

        expect($sale->warranty)
            ->product_id->toBe($accountWarrantyProduct->product_id)
            ->is_self_funded->toBe($sale->account->warranty_self_funded)
            ->provision->toBe($accountWarrantyProduct->provision)
            ->admin_fee->toBe($accountWarrantyProduct->admin_fee)
            ->selling_price->toBe($upfrontDealerPayment)
            ->monthly_selling_price->toBe($accountWarrantyProduct->monthly_selling_price)
            ->monthly_admin_fee->toBe($accountWarrantyProduct->monthly_admin_fee)
            ->isRecurring()->toBeTrue();

        expect($sale->breakdownPlan)->toBeNull();

        expect($sale->servicePlan)->toBeNull();

        expect($sale->billingRequest()->first())
            ->mandate_url->not->toBeNull()
            ->expires_at->toEqual(now()->addDays(7))
            ->visited_at->toBeNull()
            ->status->toBe('pending');

        expect($sale->payments()->count())->toBe(0);

    })->with([
        'without upfront dealer payment' => [0],
        'with upfront dealer payment' => [99],
    ]);

    test('dealers without pay later facility will not see pay later options', function () {
        $account = Account::factory()->create();
        actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

        $sale = Sale::factory()->recycle($account)->create();

        livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
            ->assertFormFieldIsHidden('is_pay_later_agreement')
            ->assertFormFieldIsHidden('payLaterAgreement.pay_later_plan_id')
            ->assertFormFieldIsHidden('payLaterAgreement.loan_amount')
            ->assertFormFieldIsHidden('payLaterAgreement.dealer_payment');
    });

    describe('dealers with pay later facility', function () {
        test('dealers with pay later facility will see pay later options', function () {
            Carbon::setTestNow(now()->startOfSecond());

            $account = Account::factory()->withPayLater()->create();
            actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

            $sale = Sale::factory()->recycle($account)->create();

            livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
                ->assertFormFieldIsVisible('is_pay_later_agreement')
                ->assertFormFieldIsEnabled('is_pay_later_agreement')
                ->assertFormFieldIsHidden('payLaterAgreement.pay_later_plan_id')
                ->assertFormFieldIsHidden('payLaterAgreement.loan_amount')
                ->assertFormFieldIsHidden('payLaterAgreement.dealer_payment')
                ->assertActionNotMounted('preApproveModal')
                ->fillForm(['is_pay_later_agreement' => true])
                ->assertFormFieldIsVisible('payLaterAgreement.pay_later_plan_id')
                ->assertFormFieldIsVisible('payLaterAgreement.loan_amount')
                ->assertFormFieldIsVisible('payLaterAgreement.dealer_payment')
                ->assertFormFieldIsDisabled('payLaterAgreement.loan_amount')
                ->assertFormFieldIsDisabled('payLaterAgreement.dealer_payment')
                ->assertActionMounted('preApproveModal');
        });

        test('pay later option is reverts to false if modal is cancelled', function () {
            Carbon::setTestNow(now()->startOfSecond());

            $account = Account::factory()->withPayLater()->create();
            actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

            $sale = Sale::factory()->recycle($account)->create();

            livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
                ->fillForm(['is_pay_later_agreement' => true])
                ->assertActionMounted('preApproveModal')
                ->call('cancelPreApproveModal')
                ->assertFormSet(['is_pay_later_agreement' => false])
                ->assertFormFieldIsVisible('is_pay_later_agreement')
                ->assertFormFieldIsEnabled('is_pay_later_agreement')
                ->assertFormFieldIsHidden('payLaterAgreement.pay_later_plan_id')
                ->assertFormFieldIsHidden('payLaterAgreement.loan_amount')
                ->assertFormFieldIsHidden('payLaterAgreement.dealer_payment');
        });

        test('pay later option is disabled if the customer fails pre-approval', function () {
            Carbon::setTestNow(now()->startOfSecond());

            $account = Account::factory()->withPayLater()->create();
            actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

            $sale = Sale::factory()->recycle($account)->create();

            mockPreApprovalCall(approved: false);

            livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
                ->fillForm(['is_pay_later_agreement' => true])
                ->assertActionMounted('preApproveModal')
                ->callMountedAction()
                ->assertFormSet(['is_pay_later_agreement' => false])
                ->assertFormFieldIsVisible('is_pay_later_agreement')
                ->assertFormFieldIsDisabled('is_pay_later_agreement')
                ->assertFormFieldIsHidden('payLaterAgreement.pay_later_plan_id')
                ->assertFormFieldIsHidden('payLaterAgreement.loan_amount')
                ->assertFormFieldIsHidden('payLaterAgreement.dealer_payment');

            expect($sale->refresh()->payLaterAgreement)
                ->toBeInstanceOf(PayLaterAgreement::class)
                ->is_approved->toBeFalse();
        });

        test('pay later option is available if the customer passes pre-approval', function () {
            Carbon::setTestNow(now()->startOfSecond());

            $account = Account::factory()->withPayLater()->create();
            actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

            $sale = Sale::factory()->recycle($account)->create();

            mockPreApprovalCall(approved: true);

            livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
                ->fillForm(['is_pay_later_agreement' => true])
                ->assertActionMounted('preApproveModal')
                ->callMountedAction()
                ->assertFormSet(['is_pay_later_agreement' => true])
                ->assertFormFieldIsVisible('is_pay_later_agreement')
                ->assertFormFieldIsEnabled('is_pay_later_agreement')
                ->assertFormFieldIsVisible('payLaterAgreement.loan_amount')
                ->assertFormFieldIsVisible('payLaterAgreement.dealer_payment');

            expect($sale->payLaterAgreement)
                ->toBeInstanceOf(PayLaterAgreement::class)
                ->is_approved->toBeTrue();
        });

        test('pay later option is disabled if the customer has already failed pre-approval', function () {
            Carbon::setTestNow(now()->startOfSecond());

            $account = Account::factory()->withPayLater()->create();
            actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

            $sale = Sale::factory()->recycle($account)->preApprovedForPayLater(approved: false)->create();

            assertNoPreApprovalCall();

            livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
                ->assertActionNotMounted('preApproveModal')
                ->assertFormFieldIsDisabled('is_pay_later_agreement');
        });

        test('pay later option is available if the customer has already passed pre-approval', function () {
            Carbon::setTestNow(now()->startOfSecond());

            $account = Account::factory()->withPayLater()->create();
            actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

            $sale = Sale::factory()->recycle($account)->preApprovedForPayLater(approved: true)->create();

            assertNoPreApprovalCall();

            livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
                ->assertActionNotMounted('preApproveModal')
                ->assertFormFieldIsEnabled('is_pay_later_agreement')
                ->assertFormFieldIsVisible('is_pay_later_agreement')
                ->assertFormFieldIsEnabled('is_pay_later_agreement')
                ->assertFormFieldIsVisible('payLaterAgreement.loan_amount')
                ->assertFormFieldIsVisible('payLaterAgreement.dealer_payment');
        });

        test('dealers selecting pay later facility need to add a plan and a loan amount', function () {
            Carbon::setTestNow(now()->startOfSecond());

            $account = Account::factory()->withPayLater()->create();
            actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
            $accountWarrantyProduct = AccountWarrantyProduct::factory()->recycle($account)->create();

            $sale = Sale::factory()->recycle($account)->preApprovedForPayLater()->create();

            livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
                ->assertFormSet(['is_pay_later_agreement' => true])
                ->fillForm([
                    'warranty.account_warranty_product_id' => $accountWarrantyProduct->id,
                    'warranty.selling_price' => 1000,
                ])
                ->assertFormSet(['total' => 1000])
                ->assertFormFieldIsVisible('payLaterAgreement.pay_later_plan_id')
                ->assertFormFieldIsVisible('payLaterAgreement.dealer_payment')
                ->assertFormFieldIsEnabled('payLaterAgreement.dealer_payment')
                ->assertFormFieldIsVisible('payLaterAgreement.loan_amount')
                ->assertFormFieldIsDisabled('payLaterAgreement.loan_amount')

                ->call('save')
                ->assertHasFormErrors([
                    'payLaterAgreement.pay_later_plan_id' => 'required',
                ]);
        });

        test('dealers with pay later facility can add a pay later plan and amount', function () {
            Carbon::setTestNow(now()->startOfSecond());

            Notification::fake();

            $account = Account::factory()->withPayLater()->create();
            actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
            $accountWarrantyProduct = AccountWarrantyProduct::factory()->recycle($account)->create();

            $sale = Sale::factory()->recycle($account)->preApprovedForPayLater()->create();

            mock(PayLaterPaymentProcessor::class, function (MockInterface $mock) use ($sale, $account) {
                $mock->shouldReceive('forAccount')
                    ->andReturn($mock);

                $mock->shouldReceive('getPlanBreakdown')
                    ->withArgs(function (PayLaterPlan $payLaterPlan, mixed $amount) use ($account) {
                        return $payLaterPlan->is($account->payLaterPlans()->first()) && $amount == 600;
                    })
                    ->andReturn(new PlanBreakdownData(
                        name: '9 Month Interest Free',
                        amount: '600.00',
                        interest: '0.00',
                        repayable: '600.00',
                        schedule: [
                            new PlanBreakdownPaymentData(date: '2025-01-01', amount: '60.00'),
                            new PlanBreakdownPaymentData(date: '2025-02-01', amount: '60.00'),
                            new PlanBreakdownPaymentData(date: '2025-03-01', amount: '60.00'),
                        ],
                    ));

                $mock->shouldReceive('startApplication')
                    ->once()
                    ->withArgs(function (PayLaterAgreement $payLaterAgreement, string $reference) use ($sale) {
                        return
                            $sale->payLaterAgreement->is($payLaterAgreement) &&
                            $reference == $sale->payLaterAgreement->getKey();
                    })
                    ->andReturn(new ApplicationRedirectData(
                        provider: PaymentProvider::PAYMENT_ASSIST,
                        token: 'SOME-TOKEN',
                        url: 'https://some-redirect-url.com',
                    ));
            });

            $payLaterPlan = $account->payLaterPlans()->first();

            livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
                ->assertFormSet(['is_pay_later_agreement' => true])
                ->fillForm([
                    'warranty.account_warranty_product_id' => $accountWarrantyProduct->id,
                    'warranty.selling_price' => 1000,
                ])
                ->assertFormSet(['total' => 1000])
                ->assertFormFieldIsVisible('payLaterAgreement.pay_later_plan_id')
                ->assertFormFieldIsVisible('payLaterAgreement.loan_amount')
                ->assertFormFieldIsDisabled('payLaterAgreement.loan_amount')
                ->assertFormFieldIsVisible('payLaterAgreement.dealer_payment')
                ->assertFormFieldIsEnabled('payLaterAgreement.dealer_payment')
                ->fillForm([
                    'payLaterAgreement.pay_later_plan_id' => $payLaterPlan->getKey(),
                    'payLaterAgreement.dealer_payment' => 400,
                ])
                ->assertFormSet(['payLaterAgreement.loan_amount' => 600])
                ->call('save')
                ->assertHasNoFormErrors();

            expect($sale->refresh())
                ->confirmed_at->toEqual(now());

            expect($sale->payLaterAgreement)
                ->toBeInstanceOf(PayLaterAgreement::class)
                ->is_approved->toBeTrue()
                ->pay_later_plan_id->toBe($payLaterPlan->getKey())
                ->dealer_payment->toEqual(400)
                ->loan_amount->toEqual(600)
                ->url->toBe('https://some-redirect-url.com')
                ->commission_rate->toBe($payLaterPlan->commission_rate)
                ->commission_rate_margin->toBe($payLaterPlan->commission_rate_margin)
                ->description->toBe($sale->description())
                ->token->toBe('SOME-TOKEN');

            Notification::assertNotSentTo($sale->customer, CustomerWelcomeAndDocumentNotification::class);
            Notification::assertSentTo($sale->customer, CustomerRequiresPaymentMethodNotification::class);
        });

        test('pay later deposit amounts are validated for minimum and maximum advances', function () {
            Carbon::setTestNow(now()->startOfSecond());

            $account = Account::factory()
                ->has(\App\Models\PayLaterPlan::factory()->state([
                    'min_amount' => 100,
                    'max_amount' => 1000,
                ]))
                ->create();

            actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));
            $accountWarrantyProduct = AccountWarrantyProduct::factory()->recycle($account)->create();

            $sale = Sale::factory()->recycle($account)->preApprovedForPayLater()->create();

            mock(PayLaterPaymentProcessor::class, function (MockInterface $mock) {
                $mock->shouldReceive('forAccount')
                    ->andReturn($mock);

                $mock->shouldReceive('getPlanBreakdown')
                    ->andReturn(new PlanBreakdownData(
                        name: '9 Month Interest Free',
                        amount: '600.00',
                        interest: '0.00',
                        repayable: '600.00',
                        schedule: [],
                    ));
            });

            livewire(AddSaleProducts::class, ['record' => $sale->getKey()])
                ->assertFormFieldIsEnabled('payLaterAgreement.dealer_payment')
                ->assertFormFieldIsDisabled('payLaterAgreement.loan_amount')
                ->assertFormFieldIsDisabled('total')
                ->fillForm([
                    'warranty.account_warranty_product_id' => $accountWarrantyProduct->id,
                    'warranty.selling_price' => 1200,
                    'is_pay_later_agreement' => true,
                    'payLaterAgreement.pay_later_plan_id' => $account->payLaterPlans()->first()->id,
                    'payLaterAgreement.dealer_payment' => 1201,
                ])
                ->call('save')
                ->assertHasFormErrors(['payLaterAgreement.dealer_payment' => 'max'])
                ->fillForm(['payLaterAgreement.dealer_payment' => -1])
                ->call('save')
                ->assertHasFormErrors(['payLaterAgreement.dealer_payment' => 'min']);
        });
    });
});
