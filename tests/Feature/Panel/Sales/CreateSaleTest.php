<?php

use App\Enums\VehicleType;
use App\Filament\Resources\SaleResource\Pages\CreateSale;
use App\Models\Account;
use App\Models\Customer;
use App\Models\Dealership;
use App\Models\Sale;
use App\Models\User;
use App\Services\VehicleLookupService\VehicleDTO;
use App\Services\VehicleLookupService\VehicleLookupService;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Mockery\MockInterface;
use OvonGroup\ManufacturerLogos\Models\Manufacturer;

use function Pest\Livewire\livewire;

test('users without permission cannot render page', function () {
    $this->actingAs(User::factory()->create());

    livewire(CreateSale::class, ['customer' => Customer::factory()->create()])
        ->assertForbidden();
});

test('users with permission can lookup a customer from new sale screen', function () {
    $account = Account::factory()->create();

    $this->actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

    $customer = Customer::factory()->recycle($account)->create();

    livewire(CreateSale::class)
        ->assertSuccessful()
        ->assertWizardCurrentStep(1)
        ->assertFormFieldIsVisible('customer_id')
        ->assertFormSet(['customer_id' => null])
        ->call('create')
        ->assertHasFormErrors(['customer_id' => 'required'])
        ->fillForm([
            'customer_id' => $customer->getKey(),
        ])
        ->call('create')
        ->assertHasNoFormErrors(['customer_id']);
});

test('users with permission can create a new sale', function (bool $isSelfFunded) {
    $this->mock(VehicleLookupService::class, function (MockInterface $mock) {
        $mock->shouldReceive('lookup')->once()
            ->withArgs(['ABC123'])
            ->andReturn(new VehicleDTO(
                vrm: 'ABC123',
                registrationDate: '2021-01-01',
                colour: 'RED',
                fuelType: 'PETROL',
                transmissionType: 'MANUAL',
                engineCapacity: 2000,
                make: 'FORD',
                model: 'FOCUS',
                derivative: 'ST-3',
                type: VehicleType::CAR,
                vin: 'WFO1234567890123',
            ));
    });

    $account = Account::factory()->create(['warranty_self_funded' => $isSelfFunded]);

    $this->actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

    $customer = Customer::factory()->recycle($account)->create();

    $manufacturer = Manufacturer::factory()->create(['name' => 'FORD']);

    livewire(CreateSale::class, ['customerId' => $customer->getKey()])
        ->assertSuccessful()
        ->assertFormSet(['customer_id' => $customer->getKey()])
        ->fillForm([
            'vrm' => 'ABC123',
        ])
        ->goToNextWizardStep()
        ->assertFormSet([
            'vrm' => 'ABC123',
            'vin' => 'WFO1234567890123',
            'manufacturer_id' => $manufacturer->getKey(),
            'vehicle_make' => 'FORD',
            'vehicle_model' => 'FOCUS',
            'vehicle_derivative' => 'ST-3',
            'vehicle_colour' => 'RED',
            'fuel_type' => 'PETROL',
            'transmission_type' => 'MANUAL',
            'vehicle_type' => VehicleType::CAR->value,
            'engine_capacity' => 2000,
            'registration_date' => '2021-01-01',
        ])
        ->goToNextWizardStep()
        ->fillForm([
            'dealership_id' => Dealership::withoutEvents(fn () => Dealership::factory()->recycle($account)->create()->getRouteKey()),
            'delivery_mileage' => 12345,
            'vehicle_price_paid' => 19995,
            'funding_method' => 'cash',
        ])
        ->call('create')
        ->assertHasNoFormErrors()
        ->assertRedirect(\App\Filament\Resources\SaleResource\Pages\AddSaleProducts::getUrl([Sale::sole()->getKey()]));

    expect(Sale::count())->toBe(1)
        ->and(Sale::sole())
        ->customer_id->toBe($customer->id)
        ->account_id->toBe($customer->account_id)
        ->vrm->toBe('ABC123')
        ->vin->toBe('WFO1234567890123')
        ->manufacturer_id->toBe($manufacturer->getKey())
        ->vehicle_make->toBe('FORD')
        ->vehicle_model->toBe('FOCUS')
        ->vehicle_derivative->toBe('ST-3')
        ->vehicle_colour->toBe('RED')
        ->fuel_type->toBe('PETROL')
        ->transmission_type->toBe('MANUAL')
        ->engine_capacity->toBe(2000)
        ->registration_date->toDateString()->toBe('2021-01-01')
        ->dealership_id->toBe(Dealership::first()->id)
        ->delivery_mileage->toBe(12345)
        ->vehicle_price_paid->toBe(19995)
        ->funding_method->toBe('cash')
        ->confirmed_at->toBeNull();
})->with([
    'managed' => false,
    'self funded' => true,
]);

test('self funded accounts will see a manufacturer text input with a dataset', function () {
    $account = Account::factory()->create([
        'warranty_self_funded' => true,
    ]);

    $this->actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

    $customer = Customer::factory()->recycle($account)->create();

    $manufacturer = Manufacturer::factory()->create();

    livewire(CreateSale::class, ['customerId' => $customer->getKey()])
        ->assertSuccessful()
        ->assertFormComponentExists(
            'data.vehicle_make',
            checkComponentUsing: fn ($component) => $component instanceof TextInput
                && $component->isVisible()
                && $component->getDatalistOptions()[0] === $manufacturer->name
        )
        ->assertFormFieldIsHidden('manufacturer_id');
});

test('managed accounts will see a manufacturer select dropdown', function () {
    $account = Account::factory()->create([
        'warranty_self_funded' => false,
    ]);

    $this->actingAs(User::factory()->recycle($account)->create()->givePermissionTo('sales.view', 'sales.create'));

    $customer = Customer::factory()->recycle($account)->create();

    $manufacturer = Manufacturer::factory()->create();

    livewire(CreateSale::class, ['customerId' => $customer->getKey()])
        ->assertSuccessful()
        ->assertFormComponentExists(
            'data.manufacturer_id',
            checkComponentUsing: fn ($component) => $component instanceof Select
                && $component->isVisible()
                && $component->getOptions()[$manufacturer->id] === $manufacturer->name
        )
        ->assertFormFieldIsHidden('vehicle_make');
});
