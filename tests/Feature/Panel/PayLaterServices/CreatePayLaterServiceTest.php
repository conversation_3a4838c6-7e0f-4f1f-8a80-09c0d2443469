<?php

use App\Actions\PayLater\BeginPayLaterApplication;
use App\Enums\VehicleType;
use App\Filament\Resources\PayLaterServiceResource\Pages\CreatePayLaterService;
use App\Models\Account;
use App\Models\Customer;
use App\Models\PayLaterAgreement;
use App\Models\PayLaterService;
use App\Models\User;
use App\Services\Payments\PayLater\PayLaterPaymentProcessor;
use App\Services\Payments\PayLater\PreApprovalData;
use App\Services\VehicleLookupService\VehicleDTO;
use App\Services\VehicleLookupService\VehicleLookupService;
use Illuminate\Database\Eloquent\Relations\Relation;
use Mockery\MockInterface;

use function Pest\Livewire\livewire;

test('users without permission cannot render page', function () {
    $this->actingAs(User::factory()->create());

    livewire(CreatePayLaterService::class, ['customer' => Customer::factory()->create()])
        ->assertForbidden();
});

test('users with permission can lookup a customer from new pay later service screen', function () {
    $account = Account::factory()->withPayLater()->create();

    $this->actingAs(User::factory()->recycle($account)->create()->givePermissionTo('pay-later-services.view', 'pay-later-services.create'));

    $customer = Customer::factory()->recycle($account)->create();

    livewire(CreatePayLaterService::class)
        ->assertSuccessful()
        ->assertWizardCurrentStep(1)
        ->assertFormFieldIsVisible('customer_id')
        ->assertFormSet(['customer_id' => null])
        ->call('create')
        ->assertHasFormErrors(['customer_id' => 'required'])
        ->fillForm([
            'customer_id' => $customer->getKey(),
        ])
        ->call('create')
        ->assertHasNoFormErrors(['customer_id']);
});

test('users with permission can create a new pay later service with vrm lookup', function () {
    $this->mock(VehicleLookupService::class, function (MockInterface $mock) {
        $mock->shouldReceive('lookup')->once()
            ->withArgs(['ABC123'])
            ->andReturn(new VehicleDTO(
                vrm: 'ABC123',
                registrationDate: '2021-01-01',
                colour: 'RED',
                fuelType: 'PETROL',
                transmissionType: 'MANUAL',
                engineCapacity: 2000,
                make: 'FORD',
                model: 'FOCUS',
                derivative: 'ST-3',
                type: VehicleType::CAR,
                vin: 'WFO1234567890123',
            ));
    });

    // Mock the PayLaterPaymentProcessor for pre-approval
    $this->mock(PayLaterPaymentProcessor::class, function (MockInterface $mock) {
        $mock->shouldReceive('forAccount')
            ->andReturn($mock);

        $mock->shouldReceive('preApprove')
            ->once()
            ->andReturn(new PreApprovalData(approved: true, message: 'Approved'));

        $mock->shouldReceive('getPlanBreakdown')
            ->andReturn(new \App\Services\Payments\PayLater\PlanBreakdownData(
                name: 'Test Plan',
                amount: '1000.00',
                interest: '50.00',
                repayable: '1050.00',
                schedule: []
            ));
    });

    // Mock the BeginPayLaterApplication action
    $this->mock(BeginPayLaterApplication::class, function (MockInterface $mock) {
        $mock->shouldReceive('execute')->once();
    });

    $account = Account::factory()->withPayLater()->create();
    $payLaterPlan = $account->payLaterPlans()->first();

    $this->actingAs(User::factory()->recycle($account)->create()->givePermissionTo('pay-later-services.view', 'pay-later-services.create'));

    $customer = Customer::factory()->recycle($account)->create();

    livewire(CreatePayLaterService::class, ['customerId' => $customer->getKey()])
        ->assertSuccessful()
        ->assertFormSet(['customer_id' => $customer->getKey()])
        ->fillForm([
            'vrm' => 'ABC123',
        ])
        ->goToNextWizardStep()
        ->assertFormSet([
            'vrm' => 'ABC123',
            'vin' => 'WFO1234567890123',
            'vehicle_make' => 'FORD',
            'vehicle_model' => 'FOCUS',
            'vehicle_derivative' => 'ST-3',
            'vehicle_colour' => 'RED',
            'fuel_type' => 'PETROL',
            'transmission_type' => 'MANUAL',
            'vehicle_type' => VehicleType::CAR->value,
            'engine_capacity' => 2000,
            'registration_date' => '2021-01-01',
        ])
        ->goToNextWizardStep()
        ->fillForm([
            'description' => 'Description of the product, service or work required',
            'pay_later_plan_id' => $payLaterPlan->getKey(),
            'invoice_amount' => 5000,
            'dealer_payment' => 1000,
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    $payLaterService = PayLaterService::first();

    expect(PayLaterService::count())->toBe(1)
        ->and($payLaterService)
        ->customer_id->toBe($customer->id)
        ->account_id->toBe($customer->account_id)
        ->vrm->toBe('ABC123')
        ->vin->toBe('WFO1234567890123')
        ->vehicle_make->toBe('FORD')
        ->vehicle_model->toBe('FOCUS')
        ->vehicle_derivative->toBe('ST-3')
        ->vehicle_colour->toBe('RED')
        ->fuel_type->toBe('PETROL')
        ->transmission_type->toBe('MANUAL')
        ->engine_capacity->toBe(2000)
        ->registration_date->toDateString()->toBe('2021-01-01')
        ->invoice_amount->toEqual(5000)
        ->dealer_payment->toEqual(1000);

    // Test that PayLaterAgreement was created
    expect(PayLaterAgreement::count())->toBe(1)
        ->and($payLaterService->payLaterAgreement)
        ->pay_later_plan_id->toBe($payLaterPlan->id)
        ->payable_type->toBe(Relation::getMorphAlias(PayLaterService::class))
        ->payable_id->toBe(PayLaterService::first()->id)
        ->description->toBe('Description of the product, service or work required')
        ->is_approved->toBe(true)
        ->pay_later_plan_id->toBe($payLaterPlan->id)
        ->dealer_payment->toBe(1000)
        ->loan_amount->toBe(4000); // invoice_amount - dealer_payment
});

test('pay later service creation populates account_id from customer', function () {
    $this->mock(PayLaterPaymentProcessor::class, function (MockInterface $mock) {
        $mock->shouldReceive('forAccount')
            ->andReturn($mock);

        $mock->shouldReceive('preApprove')
            ->once()
            ->andReturn(new PreApprovalData(approved: true, message: 'Approved'));

        $mock->shouldReceive('getPlanBreakdown')
            ->andReturn(new \App\Services\Payments\PayLater\PlanBreakdownData(
                name: 'Test Plan',
                amount: '1000.00',
                interest: '50.00',
                repayable: '1050.00',
                schedule: []
            ));
    });

    $this->mock(BeginPayLaterApplication::class, function (MockInterface $mock) {
        $mock->shouldReceive('execute')->once();
    });

    $account = Account::factory()->withPayLater()->create();
    $payLaterPlan = $account->payLaterPlans()->first();

    $this->actingAs(User::factory()->recycle($account)->create()->givePermissionTo('pay-later-services.view', 'pay-later-services.create'));

    $customer = Customer::factory()->recycle($account)->create();

    livewire(CreatePayLaterService::class, ['customerId' => $customer->getKey()])
        ->assertSuccessful()
        ->fillForm([
            'vrm' => 'ABC123',
            'vin' => 'WFO1234567890123',
            'vehicle_make' => 'FORD',
            'vehicle_model' => 'FOCUS',
            'vehicle_derivative' => 'ST-3',
            'vehicle_colour' => 'RED',
            'fuel_type' => 'PETROL',
            'transmission_type' => 'MANUAL',
            'vehicle_type' => VehicleType::CAR->value,
            'engine_capacity' => 2000,
            'registration_date' => '2021-01-01',
            'pay_later_plan_id' => $payLaterPlan->getKey(),
            'invoice_amount' => 5000,
            'dealer_payment' => 1000,
            'description' => 'Description of the product, service or work required',
        ])
        ->call('create')
        ->assertHasNoFormErrors();

    expect(PayLaterService::count())->toBe(1)
        ->and(PayLaterService::first())
        ->account_id->toBe($account->id);
});

test('pay later service creation fails when pre-approval is denied', function () {
    $this->mock(PayLaterPaymentProcessor::class, function (MockInterface $mock) {
        $mock->shouldReceive('forAccount')
            ->andReturn($mock);

        $mock->shouldReceive('preApprove')
            ->once()
            ->andReturn(new PreApprovalData(approved: false, message: 'Declined'));

        $mock->shouldReceive('getPlanBreakdown')
            ->andReturn(new \App\Services\Payments\PayLater\PlanBreakdownData(
                name: 'Test Plan',
                amount: '1000.00',
                interest: '50.00',
                repayable: '1050.00',
                schedule: []
            ));
    });

    $account = Account::factory()->withPayLater()->create();
    $payLaterPlan = $account->payLaterPlans()->first();

    $this->actingAs(User::factory()->recycle($account)->create()->givePermissionTo('pay-later-services.view', 'pay-later-services.create'));

    $customer = Customer::factory()->recycle($account)->create();

    livewire(CreatePayLaterService::class, ['customerId' => $customer->getKey()])
        ->assertSuccessful()
        ->fillForm([
            'vrm' => 'ABC123',
            'vin' => 'WFO1234567890123',
            'vehicle_make' => 'FORD',
            'vehicle_model' => 'FOCUS',
            'vehicle_derivative' => 'ST-3',
            'vehicle_colour' => 'RED',
            'fuel_type' => 'PETROL',
            'transmission_type' => 'MANUAL',
            'vehicle_type' => VehicleType::CAR->value,
            'engine_capacity' => 2000,
            'registration_date' => '2021-01-01',
            'description' => 'Description of the product, service or work required',
            'pay_later_plan_id' => $payLaterPlan->getKey(),
            'invoice_amount' => 5000,
            'dealer_payment' => 1000,
        ])
        ->assertHasNoFormErrors()
        ->call('create');

    $payLaterService = PayLaterService::first();

    expect(PayLaterService::count())->toBe(1)
        ->and(PayLaterAgreement::count())->toBe(1)
        ->and($payLaterService->payLaterAgreement)
        ->pay_later_plan_id->toBe($payLaterPlan->getKey())
        ->description->toBe('Description of the product, service or work required')
        ->is_approved->toBe(false);
});
